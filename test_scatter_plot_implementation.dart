import 'package:flutter/material.dart';
import 'dart:convert';
import 'package:syncfusion_flutter_charts/charts.dart';
import 'ui_controls_library/ui_controls_library/lib/widgets/ui_builder/flexible_widget_serializer.dart';
import 'ui_controls_library/ui_controls_library/lib/widgets/ui_builder/common/config.dart';

void main() {
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Scatter Plot Implementation Test',
      home: ScatterPlotTest(),
    );
  }
}

class ScatterPlotTest extends StatefulWidget {
  @override
  _ScatterPlotTestState createState() => _ScatterPlotTestState();
}

class _ScatterPlotTestState extends State<ScatterPlotTest> {
  ChartSizeType selectedSize = ChartSizeType.medium;
  String jsonOutput = '';
  Widget? deserializedWidget;
  bool testPassed = false;
  String testResult = '';

  @override
  void initState() {
    super.initState();
    _runTest();
  }

  void _runTest() {
    try {
      print("=== Starting Scatter Plot Implementation Test ===");
      
      // Create a scatter plot chart
      final scatterChart = _buildScatterChart(selectedSize);
      
      // Test serialization
      final serialized = FlexibleWidgetSerializer.serialize(scatterChart);
      final jsonString = const JsonEncoder.withIndent('  ').convert(serialized);
      
      print("=== Serialized Scatter Plot JSON ===");
      print(jsonString);
      
      // Test deserialization
      final rebuiltWidget = FlexibleWidgetSerializer.deserialize(serialized);
      
      setState(() {
        jsonOutput = jsonString;
        deserializedWidget = rebuiltWidget;
        testPassed = rebuiltWidget != null && rebuiltWidget is SfCartesianChart;
        testResult = testPassed 
          ? "✅ Test PASSED: Scatter Plot successfully serialized and deserialized"
          : "❌ Test FAILED: Deserialization did not produce a SfCartesianChart";
      });
      
      print("=== Test Result ===");
      print(testResult);
      
    } catch (e) {
      setState(() {
        testResult = "❌ Test FAILED with error: $e";
        testPassed = false;
      });
      print("=== Test Error ===");
      print(e);
    }
  }

  Widget _buildScatterChart(ChartSizeType size) {
    return SfCartesianChart(
      primaryXAxis: NumericAxis(
        minimum: 0,
        maximum: 35,
        interval: 5,
        axisLine: const AxisLine(width: 1),
        majorTickLines: const MajorTickLines(size: 0),
        majorGridLines: MajorGridLines(
          width: _getGridLineWidth(size),
          color: Colors.grey.shade300,
        ),
        labelStyle: TextStyle(
          fontSize: _getAxisLabelFontSize(size),
          color: Colors.black87,
        ),
        title: AxisTitle(
          text: 'Ratio%',
          textStyle: TextStyle(
            fontSize: _getAxisTitleFontSize(size),
            color: Colors.black87,
          ),
        ),
      ),
      primaryYAxis: NumericAxis(
        minimum: 2000,
        maximum: 8000,
        interval: 1000,
        axisLine: const AxisLine(width: 1),
        majorTickLines: const MajorTickLines(size: 0),
        majorGridLines: MajorGridLines(
          width: _getGridLineWidth(size),
          color: Colors.grey.shade300,
        ),
        labelStyle: TextStyle(
          fontSize: _getAxisLabelFontSize(size),
          color: Colors.black87,
        ),
        labelFormat: '\${value}K',
        title: AxisTitle(
          text: 'Sales',
          textStyle: TextStyle(
            fontSize: _getAxisTitleFontSize(size),
            color: Colors.black87,
          ),
        ),
      ),
      legend: const Legend(isVisible: false),
      tooltipBehavior: TooltipBehavior(enable: false),
      plotAreaBorderWidth: 1,
      series: <ScatterSeries<_ScatterChartData, double>>[
        ScatterSeries<_ScatterChartData, double>(
          animationDuration: 0,
          dataSource: _getScatterChartData(),
          xValueMapper: (data, _) => data.x,
          yValueMapper: (data, _) => data.y,
          pointColorMapper: (data, index) => 
              _getDefaultColors()[index % _getDefaultColors().length],
          markerSettings: MarkerSettings(
            isVisible: true,
            shape: DataMarkerType.circle,
            width: _getScatterPointSize(size),
            height: _getScatterPointSize(size),
            borderWidth: 0,
          ),
          dataLabelSettings: const DataLabelSettings(isVisible: false),
        ),
      ],
    );
  }

  // Helper methods
  double _getGridLineWidth(ChartSizeType size) {
    switch (size) {
      case ChartSizeType.small: return 0.5;
      case ChartSizeType.medium: return 0.8;
      case ChartSizeType.large: return 1.0;
    }
  }

  double _getAxisLabelFontSize(ChartSizeType size) {
    switch (size) {
      case ChartSizeType.small: return 10.0;
      case ChartSizeType.medium: return 12.0;
      case ChartSizeType.large: return 14.0;
    }
  }

  double _getAxisTitleFontSize(ChartSizeType size) {
    switch (size) {
      case ChartSizeType.small: return 11.0;
      case ChartSizeType.medium: return 13.0;
      case ChartSizeType.large: return 15.0;
    }
  }

  double _getScatterPointSize(ChartSizeType size) {
    switch (size) {
      case ChartSizeType.small: return 6.0;
      case ChartSizeType.medium: return 8.0;
      case ChartSizeType.large: return 10.0;
    }
  }

  List<_ScatterChartData> _getScatterChartData() {
    return [
      _ScatterChartData(5, 3000),
      _ScatterChartData(8, 3500),
      _ScatterChartData(12, 4000),
      _ScatterChartData(15, 4500),
      _ScatterChartData(18, 5000),
      _ScatterChartData(22, 5500),
      _ScatterChartData(25, 6000),
      _ScatterChartData(28, 6500),
      _ScatterChartData(32, 7000),
      _ScatterChartData(35, 7500),
    ];
  }

  List<Color> _getDefaultColors() {
    return [
      const Color(0xFF0D47A1), const Color(0xFF1565C0), const Color(0xFF1976D2), const Color(0xFF1E88E5),
      const Color(0xFF42A5F5), const Color(0xFF64B5F6), const Color(0xFF90CAF9), const Color(0xFFBBDEFB),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Scatter Plot Implementation Test'),
        backgroundColor: testPassed ? Colors.green : Colors.red,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Test Result
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: testPassed ? Colors.green.shade100 : Colors.red.shade100,
                border: Border.all(
                  color: testPassed ? Colors.green : Colors.red,
                  width: 2,
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                testResult,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: testPassed ? Colors.green.shade800 : Colors.red.shade800,
                ),
              ),
            ),
            
            SizedBox(height: 24),
            
            // Size Selection
            Text('Chart Size:', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
            SizedBox(height: 8),
            Wrap(
              spacing: 16.0,
              children: ChartSizeType.values.map((size) {
                return Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Radio<ChartSizeType>(
                      value: size,
                      groupValue: selectedSize,
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            selectedSize = value;
                            _runTest(); // Re-run test with new size
                          });
                        }
                      },
                    ),
                    Text(size.name.toUpperCase()),
                  ],
                );
              }).toList(),
            ),
            
            SizedBox(height: 24),
            
            // Original Chart
            if (deserializedWidget != null) ...[
              Text(
                'Deserialized Scatter Plot Chart:',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Container(
                width: double.infinity,
                height: 300,
                padding: EdgeInsets.all(16),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.blue),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: deserializedWidget!,
              ),
              SizedBox(height: 24),
            ],
            
            // JSON Output
            if (jsonOutput.isNotEmpty) ...[
              Text(
                'JSON Output:',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Container(
                width: double.infinity,
                height: 200,
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                  color: Colors.grey.shade50,
                ),
                child: SingleChildScrollView(
                  child: SelectableText(
                    jsonOutput,
                    style: TextStyle(fontSize: 12, fontFamily: 'Courier'),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

class _ScatterChartData {
  final double x;
  final double y;

  _ScatterChartData(this.x, this.y);
}
